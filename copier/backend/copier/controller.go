package copier

import (
	"copier/backend/logger"
	"copier/backend/rate"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"slices"
	"sort"
	"strings"
	"sync"
	"time"

	"copier/backend/utils"

	"github.com/PuerkitoBio/goquery"
	"github.com/jinzhu/copier"
	"github.com/puzpuzpuz/xsync"
	cron "github.com/robfig/cron/v3"
	"gopkg.in/resty.v1"
	"gopkg.in/yaml.v2"
)

type PositionSide string

const (
	PositionSideLong  PositionSide = "long"
	PositionSideShort PositionSide = "short"
)

type Position struct {
	RefID            string       `json:"refId"`
	UserID           string       `json:"userId"`
	Symbol           string       `json:"symbol"`
	Qty              float64      `json:"qty"`
	Size             float64      `json:"size"`
	EntryPrice       float64      `json:"entryPrice"`
	MarkPrice        float64      `json:"markPrice"`
	Side             PositionSide `json:"side"`
	Margin           float64      `json:"margin"`
	LiquidationPrice float64      `json:"liquidationPrice"`
	PNL              float64      `json:"pnl"`
	PNLPercentage    float64      `json:"pnlPercentage"`
	TakeProfitPrice  float64      `json:"takeProfitPrice"` // 止盈价格
	StopLossPrice    float64      `json:"stopLossPrice"`   // 止损价格
	CreateTime       *time.Time   `json:"createTime"`
}

type PositionChange struct {
	Position
	UserID     string    `json:"userId"`
	ProjectID  string    `json:"projectId"`
	CreateTime time.Time `json:"createTime"`
}

type Positions []Position

type TriggerDirection string

const (
	TriggerDirectionAbove TriggerDirection = "above"
	TriggerDirectionBelow TriggerDirection = "below"
)

type OrderStatus string

const (
	OrderStatusPending   OrderStatus = "pending"
	OrderStatusFilled    OrderStatus = "filled"
	OrderStatusCancelled OrderStatus = "cancelled"
)

type Order struct {
	Symbol           string           `json:"symbol"`
	Side             string           `json:"side"`
	Qty              float64          `json:"qty"`
	Size             float64          `json:"size"`
	Price            float64          `json:"price"`
	TriggerPrice     float64          `json:"triggerPrice"`
	TriggerDirection TriggerDirection `json:"triggerDirection"`
	Status           OrderStatus      `json:"status"`
	CreateTime       time.Time        `json:"createTime"`
	ExpireTime       time.Time        `json:"expireTime"`
}

type Role string

const (
	RoleMaster Role = "master"
	RoleCopier Role = "copier"
)

type Platform string

const (
	PlatformLighter Platform = "lighter"
)

type AgentSettings struct {
	ProjectID       string  `json:"projectId"` // 可能用于控制台修改 projectID
	DefaultSymbol   string  `json:"defaultSymbol"`
	CopyFromID      string  `json:"copyFromId"`
	CopyPercentage  int     `json:"copyPercentage"`
	MinMargin       float64 `json:"minMargin"`
	DefaultSize     float64 `json:"defaultSize"`
	Debug           bool    `json:"debug"`
	CoolingHour     float64 `json:"coolingHour"`
	RefreshInterval int     `json:"refreshInterval"`
	Alias           string  `json:"alias"`
	Version         int     `json:"version"` // timestamp, 用于判断 settings 是否变化
	ManualMode      bool    `json:"manualMode"`
	StopLossBuffer  float64 `json:"stopLossBuffer"` // 止损缓冲，用于计算止损价格, 默认 0.01，范围 0.0001-0.1
	LongRatio       float64 `json:"longRatio"`      // 多头比例，用于计算多头数量, 默认 0.5，范围 0.1-1
}

func (this *AgentSettings) ValidateStopLossBuffer() {
	if this.StopLossBuffer < 0.002 || this.StopLossBuffer > 0.1 {
		this.StopLossBuffer = 0.01
	}
}

type AgentStats struct {
	PortfolioValue float64   `json:"portfolioValue"`
	AllTimeVolume  float64   `json:"allTimeVolume"`
	AllTimeProfit  float64   `json:"allTimeProfit"`
	AllTimePoints  float64   `json:"allTimePoints"`
	CreateTime     time.Time `json:"createTime"`
}

func (this *AgentStats) IsZero() bool {
	return this.AllTimeVolume == 0 && this.AllTimeProfit == 0 && this.AllTimePoints == 0
}

type Agent struct {
	Role            Role             `json:"role"` // master/copier
	Platform        Platform         `json:"platform"`
	ProjectID       string           `json:"projectId"`
	UserID          string           `json:"userId"`
	Alias           string           `json:"alias"`
	Symbol          string           `json:"symbol"`
	CopyFromID      string           `json:"copyFromId"`
	CopyPercentage  float64          `json:"copyPercentage"`
	LastPrice       float64          `json:"lastPrice"`
	Spread          float64          `json:"spread"`
	TotalMargin     float64          `json:"totalMargin"`
	AvailableMargin float64          `json:"availableMargin"`
	Positions       []Position       `json:"positions"`
	Stats           *AgentStats      `json:"stats"`
	CreateTime      time.Time        `json:"createTime"`
	Settings        *AgentSettings   `json:"settings"`
	ServerSettings  *AgentSettings   `json:"serverSettings"` // server 端设置，如果没有比客户端更新，则始终用客户端设置覆盖
	ServerRequests  []*ServerRequest `json:"serverRequests"`
}

func (this *Agent) CleanupUserID() {
	this.UserID = strings.TrimSpace(this.UserID)
}

func (this *Agent) getShortUserID() string {
	if len(this.UserID) <= 10 {
		return this.UserID
	}
	return fmt.Sprintf("%s...%s", this.UserID[:6], this.UserID[len(this.UserID)-4:])
}

func (this *Agent) Print(prefix string) {
	table := utils.NewTable()
	table.AddRow([]string{"Object", fmt.Sprintf("%p", this)})
	table.AddRow([]string{"Role", string(this.Role)})
	table.AddRow([]string{"UserID", this.UserID})
	for _, pos := range this.Positions {
		table.AddRow([]string{"------------", "------------"})
		table.AddRow([]string{"Symbol", pos.Symbol})
		table.AddRow([]string{"Side", string(pos.Side)})
		table.AddRow([]string{"Size", fmt.Sprintf("%f", pos.Size)})
	}
	result := table.Render()
	logger.Infof("[%s] Agent: \n\n%s", prefix, result)
}

type Agents []*Agent

func (this Agents) TotalMargin() float64 {
	totalMargin := 0.0
	for _, agent := range this {
		totalMargin += agent.TotalMargin
	}
	return totalMargin
}

func (this Agents) AvailableMargin() float64 {
	availableMargin := 0.0
	for _, agent := range this {
		availableMargin += agent.AvailableMargin
	}
	return availableMargin
}

func (this Agents) Print(prefix string) {
	for _, agent := range this {
		agent.Print(prefix)
	}
}

func (this Agents) IsBalanced() bool {
	// check each agent's positions, for each symbol, check if the total size is 0, long and short should be equal
	symbolPositions := make(map[string]Positions)
	for _, agent := range this {
		for _, pos := range agent.Positions {
			symbolPositions[pos.Symbol] = append(symbolPositions[pos.Symbol], pos)
		}
	}
	for _, positions := range symbolPositions {
		if len(positions) == 0 {
			continue
		}
		totalSize := 0.0
		for _, pos := range positions {
			if pos.Side == PositionSideLong {
				totalSize += pos.Size
			} else {
				totalSize -= pos.Size
			}
		}
		isZero, _ := almostEqualTo(totalSize, 0)
		if !isZero {
			return false
		}
	}
	return true
}

type ProjectStatus string

const (
	ProjectStatusRunning ProjectStatus = "running"
	ProjectStatusStopped ProjectStatus = "stopped"
)

type ProjectStatusComment string

const (
	ProjectStatusCommentDefault             ProjectStatusComment = ""
	ProjectStatusCommentConsole             ProjectStatusComment = "console"
	ProjectStatusCommentRisk                ProjectStatusComment = "risk"
	ProjectStatusCommentExtension           ProjectStatusComment = "extension"
	ProjectStatusCommentEmergency           ProjectStatusComment = "emergency"
	ProjectStatusCommentDeleted             ProjectStatusComment = "deleted"
	ProjectStatusCommentIdle                ProjectStatusComment = "idle"
	ProjectStatusCommentAutoStopVolume      ProjectStatusComment = "auto_stop_volume"
	ProjectStatusCommentAutoStopDailyVolume ProjectStatusComment = "auto_stop_daily_volume"
	ProjectStatusCommentAutomation          ProjectStatusComment = "automation"
	ProjectStatusCommentAutomationCanceled  ProjectStatusComment = "automation_canceled"
	ProjectStatusCommentAutomationTimeout   ProjectStatusComment = "automation_timeout"
	ProjectStatusCommentAutomationPaused    ProjectStatusComment = "automation_paused"
	ProjectStatusCommentReset               ProjectStatusComment = "reset"
)

type ServerRequestFinishStatus string

const (
	ServerRequestFinishStatusSuccess  ServerRequestFinishStatus = "success"
	ServerRequestFinishStatusFailed   ServerRequestFinishStatus = "failed"
	ServerRequestFinishStatusCanceled ServerRequestFinishStatus = "canceled"
)

type ServerRequestType string

const (
	ServerRequestTypeCheckReloadPage ServerRequestType = "check_reload_page"
	ServerRequestTypeUpdateStats     ServerRequestType = "update_stats"
	ServerRequestTypeWithdrawAll     ServerRequestType = "withdraw_all"
	ServerRequestTypeNavigateToTrade ServerRequestType = "navigate_to_trade"
)

type ServerRequest struct {
	RequestID     string                    `json:"requestId"`
	ProjectID     string                    `json:"projectId"`
	UserID        string                    `json:"userId"`
	RequestType   ServerRequestType         `json:"requestType"`
	CreateTime    time.Time                 `json:"createTime"`
	FinishTime    *time.Time                `json:"finishTime"`
	FinishStatus  ServerRequestFinishStatus `json:"finishStatus"`
	FinishComment string                    `json:"finishComment"`
}

type Project struct {
	ProjectID        string               `json:"projectId"`
	Status           ProjectStatus        `json:"status"`
	StatusComment    ProjectStatusComment `json:"statusComment"`
	StatusUpdateTime time.Time            `json:"statusUpdateTime"`
	DeleteTime       *time.Time           `json:"deleteTime"`
	TotalMargin      float64              `json:"totalMargin"`
	AvailableMargin  float64              `json:"availableMargin"`
	PositionValue    float64              `json:"positionValue"`
	Volume           float64              `json:"volume"`
	Agents           Agents               `json:"agents"`
	CreateTime       time.Time            `json:"createTime"`
	BornTime         time.Time            `json:"bornTime"`
	// 这里缓存一下 performance 的数据，用于一般性的展示；如果需要展示 dailyPerformance 的详细情况，还得用 /performance/:project_id 接口
	LatestPerformance       *Performance `json:"latestPerformance"` // this week's performance
	TodayPerformance        *Performance `json:"todayPerformance"`  // today's performance
	LastPerformance         *Performance `json:"lastPerformance"`   // last week's performance
	LastTradeTime           *time.Time   `json:"lastTradeTime"`
	LastRiskTime            *time.Time   `json:"lastRiskTime"`
	LastRiskType            RiskType     `json:"lastRiskType"`
	ExternalSourcePositions []*Position  `json:"externalSourcePositions"`
}

func (this *Project) IsSafelyStopped() bool {
	return this.Status == ProjectStatusStopped && (this.IsZeroPositions() || time.Since(this.StatusUpdateTime) > 30*time.Second)
}

func (this *Project) IsIdle() bool {
	return this.Status == ProjectStatusRunning && this.StatusComment == ProjectStatusCommentIdle
}

// 如果项目属于特定的停止原因，并且停止时间是今天，则认为项目不可运行
// 否则，认为项目可以运行
func (this *CopierController) IsProjectRunnable(project *Project) bool {
	todayNotRunnable := slices.Contains([]ProjectStatusComment{
		ProjectStatusCommentRisk,
		ProjectStatusCommentAutoStopVolume,
		ProjectStatusCommentAutoStopDailyVolume,
		ProjectStatusCommentAutomationTimeout,
	}, project.StatusComment) && isWithinTodayRange(project.StatusUpdateTime)
	return !todayNotRunnable
}

func (this *Project) IsSuccessfullyFinished() bool {
	successfulFinished := slices.Contains([]ProjectStatusComment{
		ProjectStatusCommentAutoStopVolume,
		ProjectStatusCommentAutoStopDailyVolume,
	}, this.StatusComment)
	return successfulFinished && isWithinTodayRange(this.StatusUpdateTime)
}

func (this *Project) GetGroup() string {
	return strings.Split(this.ProjectID, "_")[0]
}

func (this *Project) GetMasterAgent() *Agent {
	for _, agent := range this.Agents {
		if agent.Role == RoleMaster {
			return agent
		}
	}
	return nil
}

func (this *Project) GetCopierAgent() *Agent {
	for _, agent := range this.Agents {
		if agent.Role == RoleCopier {
			return agent
		}
	}
	return nil
}

func (this *Project) IsPositionBalanced() bool {
	return this.Agents.IsBalanced()
}

func (this *Project) IsZeroPositions() bool {
	for _, agent := range this.Agents {
		if len(agent.Positions) > 0 {
			return false
		}
	}
	return true
}

type ProjectSnapshot struct {
	Project
	RefID        string    `json:"refId"`
	SnapshotTime time.Time `json:"snapshotTime"`
}

func NewProjectSnapshot(project *Project) *ProjectSnapshot {
	return &ProjectSnapshot{
		Project:      *project,
		RefID:        utils.NewRandomID(),
		SnapshotTime: time.Now(),
	}
}

type CopierController struct {
	DataDir string
	// 同一个 projectID，可能来自不同 platform，所以用 projectID 作为 key，而不是 platform_projectID
	// 比如：在 lighter 和 dydx 两个不同的平台上对冲
	Projects          *xsync.MapOf[string, *Project]     // projectId -> Project
	ProjectRiskEvents *xsync.MapOf[string, []*RiskEvent] // projectId -> RiskEvents, 单个风险事件，并不会触发报警，需要多个风险事件组合起来，才会触发报警
	ProjectRisks      *xsync.MapOf[string, []*Risk]      // projectId -> Risks, 一段时间，发生多个风险事件，可能会出发报警
	// Snapshots: projectId -> []Project, Project 是某个时间点，某个 projectID 下所有 Agents 快照
	// Project.Agents 单纯从 Projects 中拷贝过来, 写入时给一个当前时间作为 CreateTime
	// 如果 Project.Agents 中的 position 没有变化，则不会更新
	// 仅保存 balanced positions 的 Project，unbalanced 的情况，Agent 更新可能没有完成
	Snapshots *xsync.MapOf[string, []*ProjectSnapshot]
	Storage   *Storage

	checkRiskMutex   sync.Mutex
	riskRateLimiter  *xsync.MapOf[string, *rate.DurationLimiter]
	riskAlertRecords *xsync.MapOf[string, time.Time] // riskKey -> lastAlertTime, riskKey: type_projectId_userID
	// 风险事件重置时间，当某个风险解除后，设置该事件，用于过滤 event
	riskEventResetTime *xsync.MapOf[string, time.Time] // riskKey -> resetTime, riskKey: type_projectId_userID

	options *CopierOptions

	launchTime    time.Time
	lastSoundTime time.Time
	statusMutex   sync.Mutex

	enableTradeAlert    bool
	balancer            *Balancer
	automationSchedules AutomationSchedules // 每天的自动化任务，用于自动启动和停止项目
	scheduleMutex       sync.Mutex
	currentSchedule     *AutomationSchedule // 当前的自动化任务，用于自动启动和停止项目，如果为 nil，则表示没有自动化任务

	runningScheduleMutex sync.Mutex

	cronScheduler    *cron.Cron
	rotateScheduleID cron.EntryID
}

var (
	projectMutexes  = make(map[string]*sync.Mutex)
	agentMutexes    = make(map[string]*sync.Mutex)
	scheduleMutexes = make(map[string]*sync.Mutex)
	mutexMapLock    = &sync.Mutex{}
)

func getProjectMutex(projectID string) *sync.Mutex {
	mutexMapLock.Lock()
	defer mutexMapLock.Unlock()

	mutex, ok := projectMutexes[projectID]
	if !ok {
		mutex = &sync.Mutex{}
		projectMutexes[projectID] = mutex
	}
	return mutex
}

func getAgentMutex(userID string) *sync.Mutex {
	mutexMapLock.Lock()
	defer mutexMapLock.Unlock()

	mutex, ok := agentMutexes[userID]
	if !ok {
		mutex = &sync.Mutex{}
		agentMutexes[userID] = mutex
	}
	return mutex
}

func getScheduleMutex(scheduleRefID string) *sync.Mutex {
	mutexMapLock.Lock()
	defer mutexMapLock.Unlock()

	mutex, ok := scheduleMutexes[scheduleRefID]
	if !ok {
		mutex = &sync.Mutex{}
		scheduleMutexes[scheduleRefID] = mutex
	}
	return mutex
}

type CommaSeparatedString string

func (this CommaSeparatedString) Groups() []string {
	return strings.Split(string(this), ",")
}

type CopierOptions struct {
	ID                       string               `yaml:"id" json:"id"`                               // id, used to identify the copier
	Debug                    bool                 `yaml:"debug" json:"debug"`                         // debug mode
	ActivateChrome           bool                 `yaml:"activateChrome" json:"activateChrome"`       // activate chrome for agent not syncing
	EnableRemoteAlert        bool                 `yaml:"enableRemoteAlert" json:"enableRemoteAlert"` // enable remote alert
	CheckAliveURL            string               `yaml:"checkAliveURL" json:"checkAliveURL"`
	CheckAliveSecret         string               `yaml:"checkAliveSecret" json:"checkAliveSecret"`
	CheckAliveUserID         string               `yaml:"checkAliveUserID" json:"checkAliveUserID"`
	SpugAlertTemplate        string               `yaml:"spugAlertTemplate" json:"spugAlertTemplate"`
	EnableTradeAlert         bool                 `yaml:"enableTradeAlert" json:"enableTradeAlert"`                 // play "new trade" sound when a new trade is detected
	EnableBalancer           bool                 `yaml:"enableBalancer" json:"enableBalancer"`                     // enable balancer
	CheckIdleStoploss        bool                 `yaml:"checkIdleStoploss" json:"checkIdleStoploss"`               // check stoploss for idle project
	EnableStoploss           bool                 `yaml:"enableStoploss" json:"enableStoploss"`                     // global switch to enable stoploss
	EnableAutoStop           bool                 `yaml:"enableAutoStop" json:"enableAutoStop"`                     // global switch to enable auto stop
	AutoStopVolume           float64              `yaml:"autoStopVolume" json:"autoStopVolume"`                     // auto stop if volume > this value
	AutoStopDailyVolume      float64              `yaml:"autoStopDailyVolume" json:"autoStopDailyVolume"`           // auto stop if daily volume > this value
	ExtensionDir             string               `yaml:"extensionDir" json:"extensionDir"`                         // extension dir, to check extension version
	EnableAutomation         bool                 `yaml:"enableAutomation" json:"enableAutomation"`                 // enable automation
	AutomationBatchTimeout   float64              `yaml:"automationBatchTimeout" json:"automationBatchTimeout"`     // automation batch timeout hours
	AutomationBatchWait      int                  `yaml:"automationBatchWait" json:"automationBatchWait"`           // automation batch wait seconds
	AutomationBatchLimit     int                  `yaml:"automationBatchLimit" json:"automationBatchLimit"`         // number of projects in a batch
	AutomationBatchRandom    bool                 `yaml:"automationBatchRandom" json:"automationBatchRandom"`       // randomize projects in a batch
	AutomationProjectGroups  CommaSeparatedString `yaml:"automationProjectGroups" json:"automationProjectGroups"`   // automation project groups
	AutomationScriptPath     string               `yaml:"automationScriptPath" json:"automationScriptPath"`         // screen automation script path
	AutomationDailyStartTime string               `yaml:"automationDailyStartTime" json:"automationDailyStartTime"` // automation daily start time, format: HH:MM:SS
}

func (this *CopierOptions) Load(dataDir string, debugOverride *bool) error {
	yamlFile, err := os.ReadFile(filepath.Join(dataDir, "copier.yaml"))
	if err != nil {
		return fmt.Errorf("failed to read copier.yaml: %v", err)
	}
	err = yaml.Unmarshal(yamlFile, this)
	if err != nil {
		return fmt.Errorf("failed to unmarshal copier.yaml: %v", err)
	}
	// Override debug setting if command line flag is provided
	if debugOverride != nil {
		originalDebug := this.Debug
		this.Debug = *debugOverride
		if originalDebug != this.Debug {
			logger.Warnf("debug mode overridden by command line flag: %v -> %v", originalDebug, this.Debug)
		}
		logger.Warnf("debug mode overridden by command line flag: %v -> %v", originalDebug, this.Debug)
	}
	return nil
}

func (this *CopierOptions) Save(dataDir string) error {
	yamlFile, err := yaml.Marshal(this)
	if err != nil {
		return fmt.Errorf("failed to marshal copier.yaml: %v", err)
	}
	err = os.WriteFile(filepath.Join(dataDir, "copier.yaml"), yamlFile, 0644)
	if err != nil {
		return fmt.Errorf("failed to write copier.yaml: %v", err)
	}
	return nil
}

func NewCopierController(dataDir string, options *CopierOptions) (*CopierController, error) {
	if options == nil {
		options = &CopierOptions{}
		err := options.Load(dataDir, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to load copier options: %v", err)
		}
	}

	controller := &CopierController{
		DataDir:            dataDir,
		Projects:           xsync.NewMapOf[*Project](),
		ProjectRiskEvents:  xsync.NewMapOf[[]*RiskEvent](),
		ProjectRisks:       xsync.NewMapOf[[]*Risk](),
		Snapshots:          xsync.NewMapOf[[]*ProjectSnapshot](),
		Storage:            &Storage{},
		riskRateLimiter:    xsync.NewMapOf[*rate.DurationLimiter](),
		riskAlertRecords:   xsync.NewMapOf[time.Time](),
		riskEventResetTime: xsync.NewMapOf[time.Time](),
		options:            options,
		launchTime:         time.Now(),
		cronScheduler:      cron.New(cron.WithLocation(utils.GetBeijingTimezone())),
	}
	if !options.Debug {
		if options.EnableRemoteAlert {
			if controller.options.CheckAliveURL == "" {
				return nil, errors.New("checkAliveURL is required in production mode")
			}
			if controller.options.CheckAliveSecret == "" {
				return nil, errors.New("checkAliveSecret is required in production mode")
			}
			if controller.options.CheckAliveUserID == "" {
				return nil, errors.New("checkAliveUserID is required in production mode")
			}
			if controller.options.SpugAlertTemplate == "" {
				return nil, errors.New("spugAlertTemplate is required in production mode")
			}
		}
	}
	logger.InitFileLogger(dataDir)
	controller.Storage = NewStorage(controller)
	go controller.InitBalancer() // async init balancer, don't block startup
	go controller.periodicalJobs()
	controller.currentSchedule = controller.automationSchedules.GetActiveSchedule()
	go controller.RunAutomationSchedule(controller.currentSchedule)
	go controller.RunCron()
	return controller, nil
}

func (this *CopierController) RunCron() {
	this.cronScheduler.AddFunc("0 * * * *", this.CleanupAutomationSchedules)
	this.UpdateScheduleAutoStart()
	this.cronScheduler.Start()
}

func (this *CopierController) UpdateScheduleAutoStart() {
	if this.rotateScheduleID != 0 {
		this.cronScheduler.Remove(this.rotateScheduleID)
		this.rotateScheduleID = 0
	}

	if this.options.AutomationDailyStartTime != "" {
		parts := strings.Split(this.options.AutomationDailyStartTime, ":")
		if len(parts) == 2 {
			cronSpec := fmt.Sprintf("%s %s * * *", parts[1], parts[0])
			rotateScheduleID, err := this.cronScheduler.AddFunc(cronSpec, this.RotateAutomationSchedules)
			if err != nil {
				logger.Errorf("failed to add rotate automation schedule to cron: %v", err)
			} else {
				this.rotateScheduleID = rotateScheduleID
				logger.Infof("automation daily start time is updated to: %s", this.options.AutomationDailyStartTime)
			}
		} else {
			logger.Errorf("invalid automation daily start time format: %s", this.options.AutomationDailyStartTime)
		}
	}
}

func (this *CopierController) CleanupAutomationSchedules() {
	this.scheduleMutex.Lock()
	defer this.scheduleMutex.Unlock()

	// remove all schedules that are older than 3 days
	// iterate backwards to avoid slice bounds issues when removing elements
	for i := len(this.automationSchedules) - 1; i >= 0; i-- {
		schedule := this.automationSchedules[i]
		if this.currentSchedule != nil && (schedule.RefID == this.currentSchedule.RefID) {
			continue
		}
		if schedule.StartTime != nil && schedule.StartTime.Before(time.Now().Add(-24*time.Hour*8)) {
			this.automationSchedules = append(this.automationSchedules[:i], this.automationSchedules[i+1:]...)
		}
	}
	this.Storage.Save()
}

func (this *CopierController) RotateAutomationSchedules() {
	if !this.options.EnableAutomation {
		return
	}
	// skip if it's wednesday (utc)
	if time.Now().Weekday() == time.Wednesday {
		logger.Infof("skip rotate automation schedules on wednesday (utc)")
		return
	}
	if this.currentSchedule == nil || this.currentSchedule.IsEnded() {
		this.InitAutomationSchedule()
		go this.RunAutomationSchedule(this.currentSchedule)
	} else {
		this.CancelSchedule(this.currentSchedule.RefID)
	}
	this.Storage.Save()
}

func (this *CopierController) InitBalancer() {
	if this.options.EnableBalancer {
		var err error
		this.balancer, err = NewBalancer(this.DataDir)
		if err != nil {
			logger.Errorf("failed to create balancer: %v", err)
		} else {
			logger.Infof("balancer created")
		}
	}
}

func (this *CopierController) GetProjects(notDeleted bool) []*Project {
	projects := []*Project{}
	this.Projects.Range(func(key string, value *Project) bool {
		if notDeleted && value.DeleteTime != nil {
			return true
		}
		projects = append(projects, value)
		return true
	})
	// Sort projects by ProjectID for consistent ordering
	sort.Slice(projects, func(i, j int) bool {
		return projects[i].ProjectID < projects[j].ProjectID
	})
	return projects
}

func (this *CopierController) periodicalJobs() {
	go func() {
		shortTicker := time.NewTicker(5 * time.Second)
		defer shortTicker.Stop()

		for range shortTicker.C {
			this.CheckRisks()
			this.SendRiskAlert()
			this.cleanupUnfinishedServerRequests()
			if this.balancer != nil {
				this.balancer.UpdateProjects(this.GetProjects(true))
			}
			this.activateChromeForAgentNotSyncing()
		}
	}()

	go func() {
		mediumTicker := time.NewTicker(1 * time.Minute)
		defer mediumTicker.Stop()

		for range mediumTicker.C {
			this.CheckAlive()
			this.updateProjectPerformance()
			this.checkAutoStopVolume()
		}
	}()

	go func() {
		minutes := 5
		if this.options.Debug {
			minutes = 1
		}
		longTicker := time.NewTicker(time.Duration(minutes) * time.Minute)
		defer longTicker.Stop()

		for range longTicker.C {
			this.updateExternalSourcePositions()
			this.cleanupFinishedServerRequests()
		}
	}()
}

// server request 需要在 30 秒内完成，否则会给清理掉，标记为 canceled
func (this *CopierController) cleanupUnfinishedServerRequests() {
	changed := false
	for _, project := range this.GetProjects(false) {
		for _, agent := range project.Agents {
			for _, serverRequest := range agent.ServerRequests {
				// 40 秒后，如果还没有完成，则取消
				// client may take 15 seconds to process the request, so we need to wait for 40 seconds
				if serverRequest.FinishTime == nil && time.Since(serverRequest.CreateTime) > 40*time.Second {
					this.FinishServerRequest(serverRequest.RequestID, ServerRequestFinishStatusCanceled, "expired cleanup")
				}
			}
		}
	}
	if changed {
		this.Storage.Save()
	}
}

func (this *CopierController) cleanupFinishedServerRequests() {
	changed := false
	for _, project := range this.GetProjects(false) {
		newServerRequests := []*ServerRequest{}
		for _, agent := range project.Agents {
			for _, serverRequest := range agent.ServerRequests {
				if serverRequest.FinishTime != nil && time.Since(*serverRequest.FinishTime) < 10*time.Minute {
					newServerRequests = append(newServerRequests, serverRequest)
					changed = true
				}
			}
			agent.ServerRequests = newServerRequests
		}
	}
	if changed {
		this.Storage.Save()
	}
}

func (this *CopierController) updateProjectPerformance() {
	projects := this.GetProjects(true)
	for _, project := range projects {
		this.UpdateProjectPerformance(project.ProjectID)
	}
}

func (this *CopierController) StopProject(projectID string, comment ProjectStatusComment) {
	this.statusMutex.Lock()
	defer this.statusMutex.Unlock()
	project, _ := this.Projects.Load(projectID)
	if project == nil {
		logger.Errorf("project not found: %s", projectID)
		return
	}
	project.Status = ProjectStatusStopped
	project.StatusComment = comment
	project.StatusUpdateTime = time.Now()
	this.Projects.Store(projectID, project)
	this.Storage.Save()
}

func (this *CopierController) ResumeProject(projectID string, comment ProjectStatusComment) {
	this.statusMutex.Lock()
	defer this.statusMutex.Unlock()
	project, _ := this.Projects.Load(projectID)
	if project == nil {
		logger.Errorf("project not found: %s", projectID)
		return
	}
	project.Status = ProjectStatusRunning
	project.StatusComment = comment
	project.StatusUpdateTime = time.Now()
	this.Projects.Store(projectID, project)
	this.Storage.Save()
}

func (this *CopierController) _refreshCheckAlive(projectID string) {
	// curl -H "X-API-Key: " https://checkalive.changebegin.com/userID/appID/refresh
	apiSecret := this.options.CheckAliveSecret
	apiURL := this.options.CheckAliveURL
	userID := this.options.CheckAliveUserID

	if apiSecret == "" {
		logger.Errorf("check alive api secret is empty")
		return
	}

	apiURL = fmt.Sprintf("%s/%s/copier_%s/refresh", apiURL, userID, projectID)

	// use resty to request
	client := resty.New()
	resp, err := client.R().SetHeader("X-API-Key", apiSecret).Post(apiURL)
	if err != nil {
		logger.Errorf("check alive refresh request failed, error: %s", err)
		return
	}

	if resp.StatusCode() != 200 {
		logger.Errorf("check alive refresh request failed, status code: %d", resp.StatusCode())
		return
	}

	logger.Infof("check alive refresh success")
}

func (this *CopierController) CheckAlive() {
	if !this.options.EnableRemoteAlert {
		logger.Infof("remote alert is disabled, skip checking alive")
		return
	}
	// resty get request
	projectIDs := []string{}
	this.Projects.Range(func(projectID string, project *Project) bool {
		projectIDs = append(projectIDs, projectID)
		return true
	})
	for _, projectID := range projectIDs {
		this._refreshCheckAlive(projectID)
	}
}

func (this *CopierController) _sendSpugAlert(risk *Risk) {
	if !this.options.EnableRemoteAlert {
		logger.Infof("spug alert is disabled, skip sending alert: %#v", risk)
		return
	}
	logger.Infof("send spug alert: %#v", risk)
	client := resty.New()
	url := fmt.Sprintf("https://push.spug.cc/send/%s", this.options.SpugAlertTemplate)

	comment := fmt.Sprintf("项目: %s", risk.ProjectID)
	if risk.UserID != "" {
		comment += fmt.Sprintf("，用户: %s", risk.UserID)
	}

	resp, err := client.R().SetFormData(
		map[string]string{
			"type":        string(risk.Type),
			"create_time": utils.FormatShortTimeStr(&risk.CreateTime, false),
			"comment":     comment,
		},
	).Post(url)
	if err != nil {
		logger.Errorf("send spug alert failed, error: %s", err)
	}
	if resp.StatusCode() != 200 {
		logger.Errorf("send spug alert failed, status code: %d", resp.StatusCode())
	}
}

func (this *CopierController) activateChromeForAgentNotSyncing() {
	if !this.options.ActivateChrome {
		logger.Infof("activate chrome for agent not syncing is disabled, skip")
		return
	}
	if runtime.GOOS != "windows" {
		logger.Infof("activate chrome for agent not syncing is only for windows, skip")
		return
	}
	logger.Infof("activate chrome for agent not syncing")
	projectNotSyncingUserIDs := map[string][]string{} // platform -> userIDs

	for _, project := range this.GetProjects(true) {
		// 如果项目状态为 stopped 一分钟后，不激活 chrome，减少系统开销
		stopRelaxTime := project.StatusUpdateTime.Add(1 * time.Minute)
		if project.Status == ProjectStatusStopped && time.Now().After(stopRelaxTime) {
			logger.Infof("project is stopped, skip activate chrome for agent not syncing: %s", project.ProjectID)
			continue
		}
		for _, agent := range project.Agents {
			expireTime := agent.CreateTime.Add(10 * time.Second)
			if time.Now().After(expireTime) {
				projectNotSyncingUserIDs[string(agent.Platform)] = append(projectNotSyncingUserIDs[string(agent.Platform)], agent.UserID)
			}
		}
	}

	for pchromeProjectID, userIDs := range projectNotSyncingUserIDs {
		uniqueUserIDs := utils.Unique(userIDs)
		pchromes, err := this.lookupPchromeForAddress(pchromeProjectID, uniqueUserIDs)
		logger.Infof("activate chrome for agent not syncing: %s, userIDs: %s", pchromeProjectID, uniqueUserIDs)
		if err != nil {
			logger.Errorf("lookup pchrome for agent not syncing failed, projectID: %s, userIDs: %s, error: %v", pchromeProjectID, uniqueUserIDs, err)
			continue
		}
		if len(pchromes) > 0 {
			for _, pchrome := range pchromes {
				// 可能停止激活 chrome 的时候，正在循环内部，也可以跳过，否则可能要等比较长时间才能生效
				if !this.options.ActivateChrome {
					continue
				}
				err := pchrome.ActivateChrome()
				logger.Infof("activated chrome for agent not syncing: %s, projectID: %s, error: %v", pchrome.Address, pchromeProjectID, err)
				time.Sleep(500 * time.Millisecond)
			}
		}
	}
}

func (this *CopierController) activateChromeByAddress(projectID string, address string, force bool) error {
	if !force && !this.options.ActivateChrome {
		logger.Infof("activate chrome is disabled, skip")
		return nil
	}
	project, ok := this.Projects.Load(projectID)
	if !ok {
		logger.Errorf("project not found: %s", projectID)
		return fmt.Errorf("project not found: %s", projectID)
	}

	platform := ""
	for _, agent := range project.Agents {
		if agent.UserID == address {
			platform = string(agent.Platform)
			break
		}
	}

	if platform == "" {
		logger.Errorf("platform not found: %s, address: %s", projectID, address)
		return fmt.Errorf("platform not found: %s, address: %s", projectID, address)
	}

	pchromes, err := this.lookupPchromeForAddress(platform, []string{address})
	logger.Infof("activate chrome for agent not syncing: %s, address: %s", platform, address)
	if err != nil {
		logger.Errorf("lookup pchrome for agent not syncing failed, projectID: %s, address: %s, error: %v", platform, address, err)
		return fmt.Errorf("lookup pchrome for agent not syncing failed, projectID: %s, address: %s, error: %v", platform, address, err)
	}
	if len(pchromes) > 0 {
		for _, pchrome := range pchromes {
			err := pchrome.ActivateChrome()
			logger.Infof("activated chrome for agent not syncing: %s, projectID: %s, error: %v", pchrome.Address, projectID, err)
			time.Sleep(1 * time.Second)
		}
	}
	return nil
}

func (this *CopierController) SendRiskAlert() {
	_alertRisk := func(risk *Risk) {
		// send only the last risk
		logger.Warnf("send risk alert: %s, %s, %s", risk.Type, risk.ProjectID, risk.UserID)
		project, _ := this.Projects.Load(risk.ProjectID)
		// project 状态为 stopped，则不发送报警
		if project != nil && project.Status == ProjectStatusStopped {
			logger.Warnf("project is stopped, skip sending risk alert: %s, %s, %s", risk.Type, risk.ProjectID, risk.UserID)
			return
		}
		riskKey := risk.GetKey()
		this._sendSpugAlert(risk)
		message := fmt.Sprintf("%s risk alert: %s", this.options.ID, strings.ReplaceAll(string(risk.Type), "_", " "))
		this.playSound(message)
		this.riskAlertRecords.Store(riskKey, risk.CreateTime)
	}

	sameCategoryRisks := xsync.NewMapOf[[]*Risk]()
	this.ProjectRisks.Range(func(projectID string, risks []*Risk) bool {
		for _, risk := range risks {
			if risk.ResetTime != nil {
				continue
			}
			riskKey := risk.GetKey()
			risks, _ := sameCategoryRisks.LoadOrStore(riskKey, []*Risk{})
			risks = append(risks, risk)
			sameCategoryRisks.Store(riskKey, risks)
		}
		return true
	})

	sameCategoryRisks.Range(func(riskKey string, risks []*Risk) bool {
		lastAlertTime, _ := this.riskAlertRecords.LoadOrStore(riskKey, time.Time{})
		if lastAlertTime.IsZero() && len(risks) > 0 {
			lastRisk := risks[len(risks)-1]
			_alertRisk(lastRisk)
			return false
		}
		for _, risk := range risks {
			cooldown := 1 * time.Minute
			if this.options.EnableRemoteAlert {
				cooldown = 5 * time.Minute
			}
			if risk.CreateTime.After(lastAlertTime.Add(cooldown)) {
				_alertRisk(risk)
			}
		}
		return true
	})
}

func (this *CopierController) playSound(message string) {
	if time.Since(this.lastSoundTime) < 15*time.Second {
		return
	}
	this.lastSoundTime = time.Now()
	if runtime.GOOS == "darwin" {
		exec.Command("say", message).Run()
	} else if runtime.GOOS == "windows" {
		exec.Command("powershell", "-Command", fmt.Sprintf("Add-Type -AssemblyName System.Speech; (New-Object System.Speech.Synthesis.SpeechSynthesizer).Speak('%s')", message)).Run()
	}
}

func (this *CopierController) GetAgents(projectID string) (Agents, bool) {
	project, ok := this.Projects.Load(projectID)
	if !ok {
		return Agents{}, false
	}
	return project.Agents, true
}

func (this *CopierController) cloneProject(projectID string) (clonedProject *Project) {
	project, ok := this.Projects.Load(projectID)
	if !ok {
		return nil
	}
	clonedProject = &Project{}
	copier.CopyWithOption(&clonedProject, &project, copier.Option{DeepCopy: true})
	return clonedProject
}

func (this *CopierController) UpdateAgent(projectID string, newAgent *Agent) error {
	projectMutex := getProjectMutex(projectID)
	projectMutex.Lock()
	defer projectMutex.Unlock()

	// cleanup spaces and newlines in userID
	newAgent.CleanupUserID()

	agentMutex := getAgentMutex(newAgent.UserID)
	agentMutex.Lock()
	defer agentMutex.Unlock()

	if this.Storage == nil {
		logger.Errorf("storage is nil, skip updating agent")
		return fmt.Errorf("storage is nil, skip updating agent")
	}

	if len(newAgent.UserID) <= 2 {
		logger.Errorf("userID is too short(<=2), skip updating agent: %s", newAgent.UserID)
		return fmt.Errorf("userID is too short(<=2), skip updating agent: %s", newAgent.UserID)
	}

	// 克隆旧的 project，用于计算 volume 和 positionValue
	oldProject := this.cloneProject(projectID)

	nowTime := time.Now()
	newProject, _ := this.Projects.LoadOrStore(projectID, &Project{
		ProjectID:        projectID,
		CreateTime:       nowTime,
		BornTime:         nowTime,
		Status:           ProjectStatusStopped,
		StatusComment:    ProjectStatusCommentDefault,
		StatusUpdateTime: nowTime,
	})
	// logger.Infof("new project: %p, old project: %p", newProject, oldProject)
	// IMPORTANT: 如果 project 是 deleted 状态，一样也可以更新 agent
	// 会重新设置 bornTime = now, 相当于重置 project 的数据
	// 如果客户端 agent 同步导致删除后的 project 又被显示出来，只要把客户端 agent 关掉后再删除就可以了

	// create new agents instance
	agents := Agents{}
	if oldProject != nil {
		oldAgents := oldProject.Agents
		copier.CopyWithOption(&agents, &oldAgents, copier.Option{DeepCopy: true})
	}
	found := false
	for i, a := range agents {
		if a.UserID == newAgent.UserID {
			// newAgent 上传上来没有 serverSettings ，所以需要拷贝旧的 serverSettings
			// newAgent 上传上来没有 serverRequests ，所以需要拷贝旧的 serverRequests
			newAgent.ServerSettings = a.ServerSettings
			newAgent.ServerRequests = a.ServerRequests
			if newAgent.Stats == nil || newAgent.Stats.IsZero() {
				newAgent.Stats = a.Stats
			}
			agents[i] = newAgent
			found = true
			break
		}
	}
	if !found {
		agents = append(agents, newAgent)
	}

	// 如果 settings 有变化，则更新
	for i, a := range agents {
		if a.UserID == newAgent.UserID {
			// 如果客户端的 version 大于 server 的 version，则更新 server 的 settings
			if a.ServerSettings == nil {
				logger.Infof("agent server settings is nil, set client agent server settings, userID: %s, version: %d", newAgent.UserID, newAgent.Settings.Version)
				agents[i].ServerSettings = newAgent.Settings
			} else if newAgent.Settings.Version > a.ServerSettings.Version {
				logger.Infof("agent server settings is old, update server settings, userID: %s, version: %d", newAgent.UserID, newAgent.Settings.Version)
				agents[i].ServerSettings = newAgent.Settings
			}
			break
		}
	}

	if newProject.DeleteTime != nil {
		newProject.Status = ProjectStatusStopped
		newProject.StatusComment = ProjectStatusCommentDefault
		newProject.StatusUpdateTime = time.Now()
		newProject.BornTime = time.Now()
		newProject.DeleteTime = nil
	}

	newProject.TotalMargin = agents.TotalMargin()
	newProject.AvailableMargin = agents.AvailableMargin()
	newProject.Agents = agents

	// calculate volume and positionValue
	volume := 0.0
	positionValue := 0.0
	if oldProject != nil {
		volume = oldProject.Volume
	}
	// only current position value, don't consider older positions
	for _, agent := range agents {
		for _, pos := range agent.Positions {
			positionValue += pos.Size * pos.MarkPrice
		}
	}

	positionChanges := this.calculatePositionChanges(oldProject, agents)
	logger.Infof("project: %s, position changes: %v", projectID, positionChanges)
	for _, pos := range positionChanges {
		volume += pos.Size * pos.MarkPrice
		this.SavePositionChange(&pos)
	}

	newProject.Volume = volume
	newProject.PositionValue = positionValue

	// 如果 position 是平衡的，并且有持仓变化，则保存快照
	// 不保存中间状态（不平衡状态）的快照，也不保存没有持仓变化的快照，减小数据量
	if len(positionChanges) > 0 {
		now := time.Now()
		newProject.LastTradeTime = &now
		this.alertNewTrade(positionChanges)
		if newProject.IsPositionBalanced() {
			this.SaveProjectSnapshot(NewProjectSnapshot(newProject))
		}
	}

	this.Projects.Store(projectID, newProject)
	this.Storage.Save()
	return nil
}

func (this *CopierController) alertNewTrade(positionChanges []PositionChange) {
	if !this.enableTradeAlert {
		logger.Infof("trade alert is disabled, skip alerting new trade")
		return
	}
	// 如果 positionChanges 中，有新的持仓，则发送报警
	if len(positionChanges) > 0 {
		message := "new trade"
		this.playSound(message)
	}
}

// 计算 position 变化，包括持仓变化和持仓方向变化
func (this *CopierController) calculatePositionChanges(oldProject *Project, newAgents Agents) []PositionChange {
	positionChanges := []PositionChange{}

	// Create a map to track positions by symbol for each agent
	oldPositionsBySymbol := make(map[string]map[string]Position) // agent.UserID -> symbol -> position
	newPositionsBySymbol := make(map[string]map[string]Position) // agent.UserID -> symbol -> position

	// Populate old positions map
	projectID := ""
	if oldProject != nil {
		projectID = oldProject.ProjectID
		for _, agent := range oldProject.Agents {
			oldPositionsBySymbol[agent.UserID] = make(map[string]Position)
			for _, pos := range agent.Positions {
				oldPositionsBySymbol[agent.UserID][pos.Symbol] = pos
			}
		}
	}

	// Populate new positions map
	for _, agent := range newAgents {
		if projectID != "" {
			projectID = agent.ProjectID
		}
		newPositionsBySymbol[agent.UserID] = make(map[string]Position)
		for _, pos := range agent.Positions {
			newPositionsBySymbol[agent.UserID][pos.Symbol] = pos
		}
	}

	nowTime := time.Now()

	// Check for position changes
	for agentID, oldPositions := range oldPositionsBySymbol {
		newPositions, exists := newPositionsBySymbol[agentID]
		if !exists {
			// Agent no longer exists, all positions are changes
			for _, pos := range oldPositions {
				// When closing a position, we flip the side
				closedPos := pos
				if closedPos.Side == PositionSideLong {
					closedPos.Side = PositionSideShort
				} else {
					closedPos.Side = PositionSideLong
				}
				positionChanges = append(positionChanges, PositionChange{
					Position:   closedPos,
					UserID:     agentID,
					ProjectID:  projectID,
					CreateTime: nowTime,
				})
			}
			continue
		}

		// Check each symbol in old positions
		for symbol, oldPos := range oldPositions {
			newPos, exists := newPositions[symbol]
			if !exists {
				// Position closed - we should flip the side to indicate closing
				closedPos := oldPos
				if closedPos.Side == PositionSideLong {
					closedPos.Side = PositionSideShort
				} else {
					closedPos.Side = PositionSideLong
				}
				positionChanges = append(positionChanges, PositionChange{
					Position:   closedPos,
					UserID:     agentID,
					ProjectID:  projectID,
					CreateTime: nowTime,
				})
			} else if oldPos.Size != newPos.Size || oldPos.Side != newPos.Side {
				// Position modified
				if oldPos.Side != newPos.Side {
					// Side changed - record closing the old position and opening the new one
					// Close old position (flip the side)
					closedPos := oldPos
					if closedPos.Side == PositionSideLong {
						closedPos.Side = PositionSideShort
					} else {
						closedPos.Side = PositionSideLong
					}

					positionChanges = append(positionChanges, PositionChange{
						Position:   closedPos,
						UserID:     agentID,
						ProjectID:  projectID,
						CreateTime: nowTime,
					})

					// Open new position
					positionChanges = append(positionChanges, PositionChange{
						Position:   newPos,
						UserID:     agentID,
						ProjectID:  projectID,
						CreateTime: nowTime,
					})
				} else {
					// Same side, just size changed - calculate the difference
					diffPos := newPos
					diffPos.Size = newPos.Size - oldPos.Size

					// If the size difference is negative, we need to flip the side
					if diffPos.Size < 0 {
						diffPos.Size = -diffPos.Size
						if diffPos.Side == PositionSideLong {
							diffPos.Side = PositionSideShort
						} else {
							diffPos.Side = PositionSideLong
						}
					}

					positionChanges = append(positionChanges, PositionChange{
						Position:   diffPos,
						UserID:     agentID,
						ProjectID:  projectID,
						CreateTime: nowTime,
					})
				}
			}
		}
	}

	// Check for new positions that didn't exist before
	for agentID, newPositions := range newPositionsBySymbol {
		oldPositions, exists := oldPositionsBySymbol[agentID]
		if !exists {
			// New agent, all positions are changes
			for _, pos := range newPositions {
				positionChanges = append(positionChanges, PositionChange{
					Position:   pos,
					UserID:     agentID,
					ProjectID:  projectID,
					CreateTime: nowTime,
				})
			}
			continue
		}

		// Check for new positions
		for symbol, newPos := range newPositions {
			_, exists := oldPositions[symbol]
			if !exists {
				// New position opened
				positionChanges = append(positionChanges, PositionChange{
					Position:   newPos,
					UserID:     agentID,
					ProjectID:  projectID,
					CreateTime: nowTime,
				})
			}
		}
	}

	return positionChanges
}

func (this *CopierController) getRisks(projectID string, validOnly bool) []*Risk {
	risks := []*Risk{}
	validRisks := []*Risk{}
	this.ProjectRisks.Range(func(key string, value []*Risk) bool {
		for _, risk := range value {
			if projectID != "" && risk.ProjectID != projectID {
				continue
			}
			risks = append(risks, risk)
		}
		return true
	})
	if validOnly {
		for _, risk := range risks {
			if risk.CreateTime.After(this.launchTime) && risk.ResetTime == nil {
				validRisks = append(validRisks, risk)
			}
		}
		return validRisks
	} else {
		return risks
	}
}

// GetSettings returns the settings for a given project ID
func (this *CopierController) GetSettings(projectID string) (*AgentSettings, error) {
	project, ok := this.Projects.Load(projectID)
	if !ok {
		return nil, fmt.Errorf("project not found: %s", projectID)
	}

	// Find master agent
	var masterAgent *Agent
	for _, agent := range project.Agents {
		if agent.Role == RoleMaster {
			masterAgent = agent
			break
		}
	}

	if masterAgent == nil {
		return nil, fmt.Errorf("master agent not found in project: %s", projectID)
	}

	// Return a copy of the settings to prevent modification
	settings := masterAgent.Settings
	return settings, nil
}

func (this *CopierController) RenameProject(projectID string, newProjectID string) error {
	project, ok := this.Projects.Load(projectID)
	if !ok {
		return fmt.Errorf("project not found: %s", projectID)
	}
	if project.Status != ProjectStatusStopped {
		return fmt.Errorf("project is not stopped: %s", projectID)
	}
	if !project.IsZeroPositions() {
		return fmt.Errorf("project has no positions: %s", projectID)
	}
	projectMutex := getProjectMutex(projectID)
	projectMutex.Lock()
	defer projectMutex.Unlock()

	// 更新 oldProject 的 serverSettings.ProjectID ，方便在 server 端更新 projectID
	for _, agent := range project.Agents {
		agent.ServerSettings.ProjectID = newProjectID
		agent.ServerSettings.Version = int(time.Now().UnixMilli())
	}

	// create a new project copy
	newProject := &Project{}
	copier.CopyWithOption(&newProject, &project, copier.Option{DeepCopy: true})

	newProject.ProjectID = newProjectID
	newProject.Status = ProjectStatusStopped
	newProject.StatusComment = ProjectStatusComment("")
	newProject.StatusUpdateTime = time.Now()
	newProject.DeleteTime = nil
	// 更新 agent 的 projectID
	for _, agent := range newProject.Agents {
		agent.ProjectID = newProjectID
		agent.Settings.ProjectID = newProjectID
		agent.ServerSettings.ProjectID = newProjectID
	}
	// mark old project as deleted
	nowTime := time.Now()
	project.DeleteTime = &nowTime
	this.Projects.Store(projectID, project)

	// create new project
	this.Projects.Store(newProjectID, newProject)

	// copy histories
	this.copyHistories(projectID, newProjectID)

	// save storage
	this.Storage.Save()
	return nil
}

func (this *CopierController) updateExternalSourcePositions() {
	this.Projects.Range(func(projectID string, project *Project) bool {
		if !this.options.CheckIdleStoploss {
			return true
		}

		// only update positions for idle projects
		if !project.IsIdle() {
			return true
		}

		project.ExternalSourcePositions = []*Position{}
		for _, agent := range project.Agents {
			if agent.Platform != PlatformLighter {
				continue
			}
			// http client with proxy
			client := resty.New()
			pchromes, err := this.lookupPchromeForAddress(string(agent.Platform), []string{agent.UserID})
			// use proxy to get positions is mandate, if no proxy found, skip this agent
			if err != nil {
				logger.Errorf("no proxy found for address: %s", agent.UserID)
				return true
			}

			pchrome := pchromes[0]
			if pchrome.HttpProxy == "" {
				logger.Errorf("no proxy found for address: %s", agent.UserID)
				return true
			}
			client.SetProxy(pchrome.HttpProxy)
			client.SetTimeout(10 * time.Second)
			url := fmt.Sprintf("https://scan.lighter.xyz/account/%s", agent.UserID)
			resp, err := client.R().Get(url)
			if err != nil {
				logger.Errorf("failed to get positions from lighter scan: %v", err)
				return true
			}
			positions := this.parsePositionsFromLighterScan(resp.String(), agent.UserID)
			if len(positions) == 0 {
				logger.Infof("no positions found for %s", agent.UserID)
			}
			logger.Infof("has positions external source, address: %s, positions: %#v", agent.UserID, positions)
			project.ExternalSourcePositions = append(project.ExternalSourcePositions, positions...)
		}
		logger.Infof("project: %s, external source positions: %#v, project: %p", projectID, project.ExternalSourcePositions, project)
		return true
	})
}

func (this *CopierController) parsePositionsFromLighterScan(html string, userID string) []*Position {
	positions := []*Position{}

	// Create a new document from the HTML string
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		logger.Errorf("failed to parse HTML: %v", err)
		return positions
	}

	// First find the Open Positions section
	openPositionsSection := doc.Find("h1:contains('Open Positions')").Parent().Parent()
	if openPositionsSection.Length() == 0 {
		logger.Errorf("Open Positions section not found")
		return positions
	}

	// Check if there are no positions
	if openPositionsSection.Find("p:contains('No positions found')").Length() > 0 {
		return positions
	}

	// don't query actual positions, return a fake position
	// check exist of the position is enough, don't really calculate position value against this position
	position := &Position{
		UserID:     userID,
		Symbol:     "FAKE_DUMMY_SYMBOL",
		Size:       0.000,
		Side:       PositionSideLong,
		CreateTime: utils.Now(),
	}
	positions = append(positions, position)

	return positions
}

func (this *CopierController) AddServerRequest(projectID string, userID string, requestType ServerRequestType, waitSeconds int) {
	if strings.HasPrefix(projectID, "0x") {
		logger.Warnf("project id starts with 0x, be careful, projectID: %s, userID: %s, requestType: %s", projectID, userID, requestType)
	}
	projectLock := getProjectMutex(projectID)
	projectLock.Lock()
	defer projectLock.Unlock()

	agentLock := getAgentMutex(userID)
	agentLock.Lock()
	defer agentLock.Unlock()

	changed := false
	for _, project := range this.GetProjects(true) {
		if project.ProjectID == projectID {
			for _, agent := range project.Agents {
				if agent.UserID == userID {
					agent.ServerRequests = append(agent.ServerRequests, &ServerRequest{
						RequestID:   utils.NewRandomID(),
						ProjectID:   projectID,
						UserID:      userID,
						RequestType: requestType,
						CreateTime:  time.Now(),
					})
					changed = true
				}
			}
		}
	}
	if changed {
		this.Storage.Save()
	}
	if waitSeconds > 0 {
		time.Sleep(time.Duration(waitSeconds) * time.Second)
	}
}

func (this *CopierController) FinishServerRequest(requestID string, status ServerRequestFinishStatus, comment string) {
	changed := false
	for _, project := range this.GetProjects(true) {
		for _, agent := range project.Agents {
			for _, serverRequest := range agent.ServerRequests {
				if serverRequest.RequestID == requestID {
					serverRequest.FinishStatus = status
					serverRequest.FinishComment = comment
					serverRequest.FinishTime = utils.Now()
					changed = true
				}
			}
		}
	}
	if changed {
		this.Storage.Save()
	}
}

func (this *CopierController) checkAutoStopVolume() {
	if !this.options.EnableAutoStop {
		logger.Infof("auto stop is disabled, skip")
		return
	}
	dailyVolumeLimit := this.options.AutoStopDailyVolume
	if this.currentSchedule != nil && this.currentSchedule.Job == ScheduleJobDefault && this.currentSchedule.DailyVolumeLimit > 0 {
		dailyVolumeLimit = this.currentSchedule.DailyVolumeLimit
	}
	logger.Infof("check auto stop volume, daily volume limit: %f", dailyVolumeLimit)
	projects := this.GetProjects(true)
	for _, project := range projects {
		if project.Status != ProjectStatusRunning {
			continue
		}
		// TODO: check if project is balanced

		latestPerformance := &Performance{}
		if project.LatestPerformance != nil {
			withinWeek := isWithinWeekRange(project.LatestPerformance.Date)
			if withinWeek {
				latestPerformance = project.LatestPerformance
			}
		}
		todayPerformance := &Performance{}
		if project.TodayPerformance != nil {
			withinToday := isWithinTodayRange(project.TodayPerformance.Date)
			if withinToday {
				todayPerformance = project.TodayPerformance
			}
		}

		// set AutoStopVOlume == 0 to skip it
		if this.options.AutoStopVolume > 0 && latestPerformance.Volume > this.options.AutoStopVolume {
			this.StopProject(project.ProjectID, ProjectStatusCommentAutoStopVolume)
		}

		// set AutoStopDailyVolume == 0 to skip it
		if dailyVolumeLimit > 0 && todayPerformance.Volume > dailyVolumeLimit {
			this.StopProject(project.ProjectID, ProjectStatusCommentAutoStopDailyVolume)
		}
	}
}
